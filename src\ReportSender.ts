import { <PERSON>gger } from "winston";
import { Ana<PERSON>zerHandlerEvent, AnalyzerReportData, getSecret, PilatSecrets } from "./common";
import { accounts, closeDBConnection, customers, DB, initDB, interviewIdMappings, interviews } from "./drizzle";
import { eq } from "drizzle-orm";
import axios from "axios";
import { json } from "stream/consumers";


export class ReportSender {

    private logger: Logger;
    constructor(logger: Logger) {
        this.logger = logger;
    }

    async checkIfPilatReport(event: AnalyzerHandlerEvent, db: DB): Promise<boolean> {
        try {
            this.logger.info(`checkIfPilatReport:  ${JSON.stringify(event)}`);
            const { customer_id } = event;

            const account = await db.select({ accountName: accounts.name })
                .from(customers)
                .innerJoin(accounts, eq(accounts.id, customers.accountId))
                .where(eq(customers.id, customer_id));

            this.logger.info(`checkIfPilatReport: account: ${JSON.stringify(account)}`);

            return account[0].accountName.toLowerCase() === "pilat" ? true : false;

        } catch (err) {
            this.logger.error("Error in checkIfPilatReport: ", err);
            throw err;
        }

    }

    async sendReportToPilat(event: AnalyzerHandlerEvent, db: DB): Promise<void> {

        try {
            this.logger.info(`sendReportToPilat: ${JSON.stringify(event)}`);
            const pilatSecrets = await getSecret<PilatSecrets>("pilat/secrets");

            const interview = await db.select({ analyzerReportData: interviews.analyzerReport, pilatInterviewId: interviewIdMappings.pilatId })
                .from(interviews)
                .innerJoin(interviewIdMappings, eq(interviewIdMappings.interviewId, interviews.id))
                .where(eq(interviews.id, event.interview_id));

            this.logger.info(`Interview: ${JSON.stringify(interview)}`);
            const report = (interview[0].analyzerReportData as AnalyzerReportData).report_versions[0];
            this.logger.info(`Report: ${JSON.stringify(report)}`);

            const pilatId = interview[0].pilatInterviewId;

            this.logger.info(`Pilat ID: ${pilatId}`);

            const body = {
                candidate_email: pilatId,
                report
            }

            const url = pilatSecrets.PILAT_JSON_REPORT_URL;
            const apiKey = pilatSecrets.PILAT_JSON_REPORT_API_KEY
;
            const headers = {
                "x-api-key": apiKey,
            }

            const res = await axios.post(url, body, { headers });
            this.logger.info(`Response data:`);
            this.logger.info(JSON.stringify(res.data, null, 2));
            this.logger.info(`Response status: ${res.status}`);
            this.logger.info(`Response headers: ${JSON.stringify(res.headers, null, 2)}`);

            if (res.status !== 200) {
                this.logger.error(`Failed to send report, response: ${res}`);
                return;
            }
        } catch (err) {
            this.logger.error(`Error in sendReportToPilat: ${err}`);
            this.logger.error(`Failed to send report for ${JSON.stringify(event)}`);
        }
    }

    async sendReport(event: AnalyzerHandlerEvent): Promise<void> {
        try {
            const db = await initDB();
            this.logger.info("DB initialized");
            const isPilat = await this.checkIfPilatReport(event, db);
            if (isPilat) {
                await this.sendReportToPilat(event, db);
            } else {
                this.logger.info("Not a Pilat report, skipping.");
            }

        } catch (err) {
            this.logger.error(`Error in sendReport: ${err}`);
        } finally {
            await closeDBConnection();
            this.logger.info("DB connection closed");
        }
    }

}