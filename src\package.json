{"name": "hello_world", "version": "1.0.0", "description": "hello world sample for NodeJS", "main": "app.js", "repository": "https://github.com/awslabs/aws-sam-cli/tree/develop/samcli/local/init/templates/cookiecutter-aws-sam-hello-nodejs", "author": "SAM CLI", "license": "MIT", "scripts": {"unit": "jest", "lint": "eslint '*.ts' --quiet --fix", "compile": "tsc", "test": "npm run compile && npm run unit"}, "dependencies": {"@aws-sdk/client-s3": "^3.726.1", "@aws-sdk/client-secrets-manager": "^3.665.0", "@aws-sdk/client-sns": "3.687.0", "@aws-sdk/client-sqs": "^3.682.0", "axios": "^1.7.9", "dotenv": "^16.4.5", "drizzle-orm": "^0.33.0", "esbuild": "^0.14.14", "openai": "^4.78.1", "pg": "^8.13.0", "winston": "^3.16.0"}, "devDependencies": {"@jest/globals": "^29.2.0", "@types/aws-lambda": "^8.10.92", "@types/jest": "^29.2.0", "@types/node": "^20.5.7", "@types/pg": "^8.11.10", "@typescript-eslint/eslint-plugin": "^5.10.2", "@typescript-eslint/parser": "^5.10.2", "eslint": "^8.8.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.2.1", "prettier": "^2.5.1", "ts-jest": "^29.0.5", "ts-node": "^10.9.2", "typescript": "^4.8.4"}}