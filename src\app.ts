import { APIGatewayProxyResult } from "aws-lambda";
import { Logger } from "winston";
import { AnalyzerHandlerEvent, getSecret, OpenAISecret } from "./common";
import { setupLogging } from "./logger/logger";
import { Analyzer } from "./Analyzer";
import { ReportSender } from "./ReportSender";

export const lambdaHandler = async (event: any): Promise<APIGatewayProxyResult> => {
    try {
        for (const record of event.Records) {
            const event = JSON.parse(record.body) as AnalyzerHandlerEvent;
            
            const logger: Logger = setupLogging(event.run_id);
            const openAISecret = await getSecret<OpenAISecret>("openai/apikey");

            const analyzer = new Analyzer(logger, openAISecret.API_KEY);
            const reportSender = new ReportSender(logger);

            await analyzer.analyze(event);
            await reportSender.sendReport(event);
        }
        return {
            statusCode: 200,
            body: JSON.stringify({
                message: "done",
            }),
        };
    } catch (err: any) {
        return {
            statusCode: 500,
            body: JSON.stringify({ error: err.stack }),
        };
    }
};
