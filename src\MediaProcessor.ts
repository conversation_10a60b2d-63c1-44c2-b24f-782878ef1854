import * as fs from "fs";
import * as path from "path";
import * as os from "os";
import * as crypto from "crypto";
import axios from "axios";
import { S3Client, GetObjectCommand, PutObjectCommand } from "@aws-sdk/client-s3";
import { SecretsManagerClient, GetSecretValueCommand } from "@aws-sdk/client-secrets-manager";
import { exec, execFile } from "child_process";
import * as util from "util";
import { URL } from "url";
import { v4 as uuid } from "uuid";
import openai from "openai";
import { Logger } from "winston";
import { retry, sleep } from "./common";

const execAsync = util.promisify(exec);
const execFileAsync = util.promisify(execFile);

export class MediaProcessor {
    private s3Client: S3Client;
    private tempDir: string;
    private cloudfrontUrlPrefix: string = process.env.CLOUDFRONT_URL!;
    private interviewerBucket: string = process.env.INTERVIEWER_UPLOADS_BUCKET!;
    private targetLanguage: string = process.env.TARGET_LANGUAGE!;
    private translationModel: string = process.env.TRANSLATION_MODEL ? process.env.TRANSLATION_MODEL : "gpt-4o";
    private s3Bucket: string = process.env.S3_BUCKET!;
    private videoFileSuffix: string = "mp4";
    private audioFileSuffix: string = "mp3";
    private openaiClient: openai;
    private logger: Logger;
    private maxVideoLengthThreshold = process.env.MAX_VIDEO_LENGTH_THRESHOLD ? parseInt(process.env.MAX_VIDEO_LENGTH_THRESHOLD) : 121;
    private minVideoLengthThreshold = process.env.MIN_VIDEO_LENGTH_THRESHOLD ? parseInt(process.env.MIN_VIDEO_LENGTH_THRESHOLD) : 10;

    constructor(logger: Logger, tempDir: string, openai: openai) {
        const region = process.env.AWS_REGION;
        this.s3Client = new S3Client({ region });
        this.tempDir = tempDir;
        this.openaiClient = openai;
        this.logger = logger;
    }

    private prepareQuestionForKey(question: string): string {
        question = question.replace(/ /g, "_");
        question = question.replace(/\//g, "_or_");
        question = question.replace(/[.,?]/g, "");

        return question;
    }

    async downloadVideoFromS3(videoKey: string): Promise<string> {
        const localPath = path.join(this.tempDir, path.basename(videoKey));
        const command = new GetObjectCommand({ Bucket: this.interviewerBucket, Key: videoKey });
        const response = await this.s3Client.send(command);

        if (!response.Body) {
            this.logger.error(`Failed to download video from S3: ${videoKey}`);
            return "";
        }

        const bodyContents = await response.Body.transformToByteArray();
        fs.writeFileSync(localPath, Buffer.from(bodyContents));
        return localPath;
    }
    async downloadVideoFromUrl(videoUrl: string, retries = 3, delay = 5000): Promise<string> {
        const filePath = path.join(this.tempDir, `${uuid()}.${this.videoFileSuffix}`);

        // Ensure the temporary directory exists
        if (!fs.existsSync(this.tempDir)) {
            fs.mkdirSync(this.tempDir, { recursive: true });
            this.logger.info(`Created temporary directory: ${this.tempDir}`);
        }

        this.logger.info(`Attempting to download video from ${videoUrl} to ${filePath}`);

        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                this.logger.info(`Attempt ${attempt} to download video.`);

                // Clean up any existing file before retry
                if (fs.existsSync(filePath)) {
                    fs.unlinkSync(filePath);
                    this.logger.info(`Removed existing file at: ${filePath}`);
                }

                // Make the HTTP request
                const response = await axios.get(videoUrl, {
                    responseType: "arraybuffer",
                });

                if (fs.existsSync(this.tempDir)) {
                    this.logger.info(`Directory listing of ${this.tempDir}: ${fs.readdirSync(this.tempDir)}`);
                }

                await fs.promises.writeFile(filePath, response.data);
                this.logger.info(`Successfully downloaded video to ${filePath}`);
                return filePath;
            } catch (error: any) {
                this.logger.error(`Attempt ${attempt} failed: ${error.message}`);
                if (attempt === retries) {
                    throw new Error(`Failed to download video from ${videoUrl} after ${retries} attempts.`);
                }

                const backoff = Math.pow(2, attempt) * delay;
                this.logger.info(`Retrying in ${backoff / 1000} seconds...`);
                await new Promise((resolve) => setTimeout(resolve, backoff));
            }
        }

        return "";
    }

    isUrlOrFilePath(inputString: string): string {
        if (!inputString) {
            return "unknown";
        }

        try {
            const parsed = new URL(inputString);
            if (parsed.protocol && parsed.host) {
                return "url";
            }
        } catch {
            return "file";
        }

        return "unknown";
    }

    @retry()
    async downloadVideo(videoUrl: string): Promise<string> {
        if (this.isUrlOrFilePath(videoUrl) === "url") return await this.downloadVideoFromUrl(videoUrl);
        else if (this.isUrlOrFilePath(videoUrl) === "file") return await this.downloadVideoFromS3(videoUrl);
        else throw new Error(`Unknown input type for video URL: ${videoUrl}`);
    }

    @retry()
    async uploadFileToS3(filePath: string, interviewId: string, questionText: string): Promise<string> {
        this.logger.info(`Uploading file to S3: ${filePath}`);
        const fileContent = fs.readFileSync(filePath);

        const key = `interviews/${interviewId}/${this.prepareQuestionForKey(questionText)}.${this.videoFileSuffix}`;

        const command = new PutObjectCommand({
            Bucket: this.s3Bucket,
            Key: key,
            Body: fileContent,
            ContentType: "video/mp4",
        });

        await this.s3Client.send(command);
        this.logger.info(`Uploaded file to S3: ${filePath}`);
        return key;
    }

    @retry()
    async convertVideoToAudio(videoPath: string): Promise<string> {
        const audioPath = videoPath.replace(path.extname(videoPath), `.${this.audioFileSuffix}`);
        await execAsync(`ffmpeg -i ${videoPath} -q:a 0 -map a ${audioPath}`);
        return audioPath;
    }

    @retry()
    async transcribeAudio(audioPath: string, sourceLanguage: string): Promise<string> {
        try {
            const response = await this.openaiClient.audio.transcriptions.create({
                file: fs.createReadStream(audioPath),
                model: "whisper-1",
                language: sourceLanguage,
            });

            this.logger.info(`Transcription: ${response.text}`);
            return response.text;
        } catch (error: any) {
            this.logger.error(`Error transcribing audio: ${error.message}`);
            throw error;
        }
    }

    @retry()
    async translateText(text: string, sourceLanguage: string): Promise<string> {
        if (sourceLanguage === "en") {
            this.logger.info("Text is already in English");
            return text;
        }
        const response = await this.openaiClient.chat.completions.create({
            model: this.translationModel,
            temperature: 0.3,
            max_tokens: 1000,
            messages: [
                { role: "system", content: "You are an excellent translator from Hebrew to English." },
                {
                    role: "user",
                    content: "Translate the following text from Hebrew to English. Do not add anything else. \nHebrew Text:" + text,
                },
            ],
        });

        const translation = response.choices[0].message.content ? response.choices[0].message.content : "";

        this.logger.info(`Translation: ${translation}`);

        return translation;
    }
    @retry()
    async getVideoLength(filePath: string): Promise<number> {
        const cmd = ["ffprobe", "-v", "quiet", "-print_format", "json", "-show_format", "-show_streams", filePath];

        try {
            const { stdout, stderr } = await execFileAsync(cmd[0], cmd.slice(1));

            if (stderr) {
                throw new Error(`ffprobe error: ${stderr}`);
            }

            const formatInfo = JSON.parse(stdout);
            const duration = parseFloat(formatInfo["format"]["duration"]);

            return duration;
        } catch (error) {
            this.logger.error(`Error getting video length: ${error}`);
            throw error;
        }
    }

    @retry()
    async addTextToVideo(questionId: string | null, inputVideoPath: string, text: string, sourceLanguage: string, maxWordsPerLine: number = 8): Promise<string> {
        try {
            if (!fs.existsSync(inputVideoPath)) {
                throw new Error(`Input video file does not exist: ${inputVideoPath}`);
            }

            let textToAdd = text;

            if (sourceLanguage !== "en" && !questionId) {
                this.logger.info(`Translating text to English: ${text}`);
                textToAdd = await this.translateText(text, sourceLanguage);
            }
            

            this.logger.info(`Adding text to video: ${inputVideoPath}`);

            const words = textToAdd.split(" ");
            const wrappedLines = words
                .reduce((lines: string[][], word, index) => {
                    if (index % maxWordsPerLine === 0) lines.push([]);
                    lines[lines.length - 1].push(word);
                    return lines;
                }, [])
                .map((line) => line.join(" "));

            const ffmpegText = wrappedLines.join("\r").replace(/'/g, "''");

            const outputVideoPath = inputVideoPath.replace(".mp4", "_text.mp4");

            const commandArgs = [
                "-i",
                inputVideoPath,
                "-vf",
                `drawtext=fontfile=/opt/fonts/font.ttf:text='${ffmpegText}':x=(w-text_w)/2:y=h-text_h-50:fontsize=20:fontcolor=yellow@1`,
                "-c:v",
                "libx264",
                "-preset",
                "ultrafast",
                "-crf",
                "30",
                "-c:a",
                "aac",
                "-b:a",
                "96k",
                "-y",
                "-threads",
                "4",
                outputVideoPath,
            ];

            this.logger.info(`Running FFmpeg command: ffmpeg ${commandArgs.join(" ")}`);

            await execFileAsync("ffmpeg", commandArgs);

            this.logger.info(`Successfully added text to video: ${outputVideoPath}`);
            return outputVideoPath;
        } catch (error: any) {
            this.logger.error(`Error in addTextToVideo: ${error.message}`);
            throw error;
        }
    }

    async processVideo(
        questionId: string | null,
        videoKey: string,
        interviewId: string,
        questionText: string,
        sourceLanguage: string
    ): Promise<{ transcription: string; videoLink: string; translation: string }> {
        try {
            const videoPath = await this.downloadVideo(videoKey);

            if (!videoPath) {
                this.logger.warn(`Having and issue with downloading : ${videoKey}`);
                return { transcription: "", videoLink: "", translation: "" };
            }

            const videoLength = await this.getVideoLength(videoPath);

            this.logger.info(`Video length: ${videoLength}`);

            const videoWithTextPath = await this.addTextToVideo(questionId,videoPath, questionText, sourceLanguage);

            const key = await this.uploadFileToS3(videoWithTextPath, interviewId, questionText);
            const videoLink = `${this.cloudfrontUrlPrefix}/${key}`;

            if (videoLength < this.minVideoLengthThreshold || videoLength > this.maxVideoLengthThreshold) {
                this.logger.warn(`Invalid video length: ${videoKey}`);
                return { transcription: "", videoLink, translation: "" };
            }

            const audioPath = await this.convertVideoToAudio(videoPath);

            const transcription = await this.transcribeAudio(audioPath, sourceLanguage);
            const translation = await this.translateText(transcription, sourceLanguage);

            return { transcription, translation, videoLink };
        } catch (error) {
            console.error("Error processing video:", error);
            throw error;
        }
    }
}
