import { Logger } from "winston";
import {
    AnalyzerDataForEvaluation,
    AnalyzerHandlerEvent,
    AnalyzerTextCorrection,
    ComputedTrait,
    GradingPrinciple,
    GradingPrinciplesMap,
    InterviewQuestionAnswer,
    Language,
    LanguageVariation,
    QuestionAnswerPairForAnalyzer,
    QuestionForAnalyzer,
    QuestionForProcessingAndAnswerVideoLink,
    retry,
    sendSNSMessage,
    TemplatesMap,
} from "./common";
import {
    answers,
    candidates,
    closeDBConnection,
    customers,
    DB,
    initDB,
    interviews,
    interviewSummarySentences,
    jobs,
    promptTemplates,
    questions,
    traits,
    traitsFamilies,
    translationCorrections,
} from "./drizzle";
import { and, eq, inArray, isNotNull } from "drizzle-orm";
import { MediaProcessor } from "./MediaProcessor";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";
import openai from "openai";

import { AnalyzedAnswerResult, AnswerTranscription, QuestionDataRequiredForAnalyzer } from "./types";
import { Family, ReportVersion, Trait } from "./common/types/AnalyzerReportData";
import { ChatCompletionCreateParamsNonStreaming } from "openai/resources";

export class Analyzer {
    logger: Logger;
    tempDir: string = path.join(os.tmpdir(), "analyzer-temp");
    openaiClient: openai;

    cloudfrontDistributionURL: string = process.env.CLOUDFRONT_URL!;

    shouldGradeAnswerModel: string = process.env.SHOULD_GRADE_ANSWER_MODEL!;
    shouldGradeAnswerTemperature: number = process.env.SHOULD_GRADE_ANSWER_TEMPERATURE
        ? Number(process.env.SHOULD_GRADE_ANSWER_TEMPERATURE)
        : 0;
    shouldGradeAnswerTokens: number = process.env.SHOULD_GRADE_ANSWER_TOKENS
        ? Number(process.env.SHOULD_GRADE_ANSWER_TOKENS)
        : 5;
    shouldGradeAnswerTemplate: string = process.env.SHOULD_GRADE_ANSWER_TEMPLATE!;

    gradeAnswerModel: string = process.env.GRADE_ANSWER_MODEL!;
    gradeAnswerTemperature: number = process.env.GRADE_ANSWER_TEMPERATURE
        ? Number(process.env.GRADE_ANSWER_TEMPERATURE)
        : 0;
    gradeAnswerTokens: number = process.env.GRADE_ANSWER_TOKENS ? Number(process.env.GRADE_ANSWER_TOKENS) : 5;
    gradeAnswerTemplate: string = process.env.GRADE_ANSWER_TEMPLATE!;

    calculateTraitScoreModel: string = process.env.CALCULATE_TRAIT_SCORE_MODEL!;
    calculateTraitScoreTemperature: number = process.env.CALCULATE_TRAIT_SCORE_TEMPERATURE
        ? Number(process.env.CALCULATE_TRAIT_SCORE_TEMPERATURE)
        : 0;
    calculateTraitScoreTokens: number = process.env.CALCULATE_TRAIT_SCORE_TOKENS
        ? Number(process.env.CALCULATE_TRAIT_SCORE_TOKENS)
        : 5;
    calculateTraitScoreTemplate: string = process.env.CALCULATE_TRAIT_SCORE_TEMPLATE!;
    calculateTraitScorePrincipleTemplate: string = process.env.CALCULATE_TRAIT_SCORE_PRINCIPLE_TEMPLATE!;
    calculateTraitScorePrincipleTemplateZeroScoreMessage =
        process.env.CALCULATE_TRAIT_SCORE_PRINCIPLE_TEMPLATE_ZERO_SCORE_MESSAGE ||
        "Was not able to score, not enough information was provided";

    traitSummaryModel: string = process.env.TRAIT_SUMMARY_MODEL!;
    traitSummaryExplanationTemplate: string = process.env.TRAIT_SUMMARY_EXPLANATION_TEMPLATE!;
    traitSummaryTemperature: number = process.env.TRAIT_SUMMARY_TEMPERATURE
        ? Number(process.env.TRAIT_SUMMARY_TEMPERATURE)
        : 0;
    traitSummaryTokens: number = process.env.TRAIT_SUMMARY_TOKENS ? Number(process.env.TRAIT_SUMMARY_TOKENS) : 600;
    traitSummaryIntroComposerTemplate: string = process.env.TRAIT_SUMMARY_INTRO_COMPOSER_TEMPLATE!;
    traitSummaryIntroTemplate: string = process.env.TRAIT_SUMMARY_INTRO_TEMPLATE!;
    traitSummaryEndingTemplate: string = process.env.TRAIT_SUMMARY_ENDING_TEMPLATE!;
    traitSummaryHebrewTemplate: string = process.env.TRAIT_SUMMARY_HEBREW_TEMPLATE!;

    familySummaryModel: string = process.env.FAMILY_SUMMARY_MODEL!;
    familySummaryTemplate: string = process.env.FAMILY_SUMMARY_TEMPLATE!;
    familySummaryTemperature: number = process.env.FAMILY_SUMMARY_TEMPERATURE
        ? Number(process.env.FAMILY_SUMMARY_TEMPERATURE)
        : 0.3;
    familySummaryTokens: number = process.env.FAMILY_SUMMARY_TOKENS ? Number(process.env.FAMILY_SUMMARY_TOKENS) : 600;
    familySummaryCorrectionTemplate: string = process.env.FAMILY_SUMMARY_CORRECTION_TEMPLATE!;
    familySummaryCorrectionSystemTemplate: string = process.env.FAMILY_SUMMARY_CORRECTION_SYSTEM_TEMPLATE!;
    familySummaryCorrectionLanguageGenderTemplate: string =
        process.env.FAMILY_SUMMARY_LANGUAGE_GENDER_CORRECTION_TEMPLATE!;
    familySummaryCorrectionLanguageGenderSystemContent: string =
        process.env.FAMILY_SUMMARY_GENDER_CORRECTION_SYSTEM_CONTENT!;
    familySummaryCorrectionEnglishTemperature: number = process.env.FAMILY_SUMMARY_CORRECTION_ENGLISH_TEMPERATURE
        ? Number(process.env.FAMILY_SUMMARY_CORRECTION_ENGLISH_TEMPERATURE)
        : 0.5;
    familySummaryCorrectionEnglishTokens: number = process.env.FAMILY_SUMMARY_CORRECTION_ENGLISH_TOKENS
        ? Number(process.env.FAMILY_SUMMARY_CORRECTION_ENGLISH_TOKENS)
        : 600;
    familySummaryCorrectionHebrewTemperature: number = process.env.FAMILY_SUMMARY_CORRECTION_HEBREW_TEMPERATURE
        ? Number(process.env.FAMILY_SUMMARY_CORRECTION_HEBREW_TEMPERATURE)
        : 0.2;
    familySummaryCorrectionHebrewTokens: number = process.env.FAMILY_SUMMARY_CORRECTION_HEBREW_TOKENS
        ? Number(process.env.FAMILY_SUMMARY_CORRECTION_HEBREW_TOKENS)
        : 600;

    familySummaryGenderCorrectionTemperature: number = process.env.FAMILY_SUMMARY_GENDER_CORRECTION_TEMPERATURE
        ? Number(process.env.FAMILY_SUMMARY_GENDER_CORRECTION_TEMPERATURE)
        : 0;
    familySummaryGenderCorrectionTokens: number = process.env.FAMILY_SUMMARY_GENDER_CORRECTION_TOKENS
        ? Number(process.env.FAMILY_SUMMARY_GENDER_CORRECTION_TOKENS)
        : 1000;

    verbalAbilityTraitId: string = process.env.VERBAL_ABILITY_TRAIT_ID!;
    verbalAbilityTemplate: string = process.env.VERBAL_ABILITY_TEMPLATE!;
    verbalAbilityModel: string = process.env.VERBAL_ABILITY_MODEL!;
    verbalAbilityTemperature: number = process.env.VERBAL_ABILITY_TEMPERATURE
        ? Number(process.env.VERBAL_ABILITY_TEMPERATURE)
        : 0;
    verbalAbilityTokens: number = process.env.VERBAL_ABILITY_TOKENS ? Number(process.env.VERBAL_ABILITY_TOKENS) : 2;

    staticSystemInput: string = "You are an expert occupational psychologist";
    notGradableFamilyMessageMap: LanguageVariation = {
        en: process.env.NOT_GRADABLE_FAMILY_MESSAGE_ENGLISH || "Not enough information to evaluate candidates traits.",
        he: process.env.NOT_GRADABLE_FAMILY_MESSAGE_HEBREW || "לא ניתן להעריך את התכונות עקב חוסר מידע.",
    };

    notGradableAnswerMessage: Record<string, string> = {
        en: process.env.NOT_GRADABLE_ANSWER_MESSAGE_ENGLISH || "The answer is not gradable due to lack of information",
        he: process.env.NOT_GRADABLE_ANSWER_MESSAGE_HEBREW || "לא ניתן להעריך את התושבה עקב חוסר מידע",
    };

    scoreToVerbalValue: Record<string, Record<number, string>> = {
        en: {
            1: "lowest",
            2: "below average",
            3: "average",
            4: "above average",
            5: "highest",
        },
        he: {
            1: "הנמוך ביותר",
            2: "נמוך",
            3: "ממוצע",
            4: "גבוה",
            5: "הגבוה ביותר",
        },
    };

    judgeTemplate: string = process.env.JUDGE_TEMPLATE!;
    judgeModel: string = process.env.JUDGE_MODEL!;

    constructor(logger: Logger, openaiApiKey: string) {
        this.logger = logger;
        this.openaiClient = new openai({ apiKey: openaiApiKey });

        if (!fs.existsSync(this.tempDir)) {
            fs.mkdirSync(this.tempDir, { recursive: true });
        }
    }

    private splitAnswersForProcessingAndNotProcessing(questionsAndAnswers: QuestionForProcessingAndAnswerVideoLink[]): {
        questionsForProcessing: QuestionForProcessingAndAnswerVideoLink[];
        openQuestions: QuestionForProcessingAndAnswerVideoLink[];
        warmupQuestions: QuestionForProcessingAndAnswerVideoLink[];
    } {
        const questionsForProcessing: QuestionForProcessingAndAnswerVideoLink[] = [];
        const openQuestions: QuestionForProcessingAndAnswerVideoLink[] = [];
        const warmupQuestions: QuestionForProcessingAndAnswerVideoLink[] = [];

        for (const questionAndAnswer of questionsAndAnswers) {
            if (questionAndAnswer.question_id === null && !questionsForProcessing.length) {
                warmupQuestions.push(questionAndAnswer);
            } else if (questionAndAnswer.question_id !== null) {
                questionsForProcessing.push(questionAndAnswer);
            } else {
                openQuestions.push(questionAndAnswer);
            }
        }

        return { questionsForProcessing, openQuestions, warmupQuestions };
    }

    private async promptOpenAIChatModel(
        system: string,
        userInput: string,
        model: string,
        maxTokens: number,
        temperature: number
    ): Promise<string> {
        const response = await this.openaiClient.chat.completions.create({
            model,
            messages: [
                {
                    role: "system",
                    content: system,
                },
                {
                    role: "user",
                    content: userInput,
                },
            ],
            max_tokens: maxTokens,
            temperature,
        });
        return response.choices[0].message.content || "";
    }

    @retry()
    private async changeInterviewStatusToRunning(interviewId: string): Promise<void> {
        this.logger.info("Changing interview status to running", interviewId);
        try {
            const db = await initDB();
            await db.update(interviews).set({ status: "running" }).where(eq(interviews.id, interviewId));
        } catch (err) {
            this.logger.error("Error changing interview status to running", err);
            throw err;
        } finally {
            await closeDBConnection();
        }
    }

    cleanResources(): void {
        fs.rmSync(this.tempDir, { recursive: true, force: true });
    }

    async transcriptAnswers(
        questionAnswerPairs: QuestionForProcessingAndAnswerVideoLink[],
        language: string,
        interviewId: string
    ): Promise<AnswerTranscription[]> {
        try {
            if (!questionAnswerPairs.length) return [];

            const mediaProcessor = new MediaProcessor(this.logger, this.tempDir, this.openaiClient);

            const transcriptions: AnswerTranscription[] = await Promise.all(
                questionAnswerPairs.map(async (pair) => {
                    const { transcription, videoLink, translation } = await mediaProcessor.processVideo(
                        pair.question_id,
                        pair.video_link,
                        interviewId,
                        pair.text,
                        language
                    );
                    return {
                        questionId: pair.question_id,
                        url: videoLink,
                        transcription,
                        question: pair.text,
                        translatedTranscription: translation,
                    };
                })
            );

            return transcriptions;
        } catch (err) {
            this.logger.error("Error transcribing answers", err);
            throw err;
        }
    }

    @retry()
    async fetchQuestionsData(questionIds: string[], db: DB): Promise<QuestionDataRequiredForAnalyzer[]> {
        try {
            this.logger.info(`FETCHING QUESTIONS DATA FOR QUESTION IDS: ${questionIds}`);
            const res = await db
                .select({
                    questionOptions: questions.options,
                    traitName: traits.trait,
                    traitFamilyName: traitsFamilies.family,
                    questionId: questions.id,
                    traitId: traits.id,
                    traitFamilyId: traitsFamilies.id,
                    traitSummariesMap: traits.traitSummariesMap,
                    questionGradingPrinciples: questions.gradingPrinciples,
                    familyPromptMetadata: traitsFamilies.promptMetadata,
                    traitPromptMetadata: traits.promptMetadata,
                })
                .from(questions)
                .innerJoin(traits, and(eq(traits.id, questions.traitId), isNotNull(traits.traitSummariesMap)))
                .innerJoin(traitsFamilies, eq(traitsFamilies.id, traits.familyId))
                .where(inArray(questions.id, questionIds));

            this.logger.info(`FETCHED QUESTIONS DATA: ${JSON.stringify(res)}`);

            const sorted = res.sort((a, b) => questionIds.indexOf(a.questionId) - questionIds.indexOf(b.questionId));

            return sorted as QuestionDataRequiredForAnalyzer[];
        } catch (err) {
            this.logger.error("Error fetching questions data", err);
            throw err;
        }
    }

    @retry()
    async getPromptTemplatesMap(db: DB): Promise<TemplatesMap> {
        this.logger.info("Fetching prompt templates map");

        const res = await db
            .select({
                language: promptTemplates.language,
                template: promptTemplates.template,
                type: promptTemplates.type,
            })
            .from(promptTemplates);

        const templatesMap: TemplatesMap = { en: {}, he: {} };

        for (const item of res) {
            templatesMap[item.language as keyof TemplatesMap][item.type] = item.template;
        }

        this.logger.info(`Prompt templates map: ${JSON.stringify(templatesMap)}`);

        return templatesMap;
    }

    @retry(3, 300)
    async fetchTextCorrections(db: DB, language: string): Promise<AnalyzerTextCorrection[]> {
        try {
            return await db
                .select({ text: translationCorrections.text, correction: translationCorrections.correction })
                .from(translationCorrections)
                .where(eq(translationCorrections.language, language));
        } catch (err) {
            this.logger.error("Error fetching text corrections", err);
            throw err;
        }
    }

    @retry(3, 300)
    async fetchInterviewSummaries(db: DB, language: string): Promise<Record<number, string[]>> {
        try {
            const res = await db
                .select({ score: interviewSummarySentences.score, sentence: interviewSummarySentences.sentence })
                .from(interviewSummarySentences)
                .where(eq(interviewSummarySentences.language, language));

            return res.reduce((acc, item) => {
                if (!acc[item.score]) {
                    acc[item.score] = [];
                }
                acc[item.score].push(item.sentence);
                return acc;
            }, {} as Record<number, string[]>);
        } catch (err) {
            this.logger.error("Error fetching interview summaries", err);
            throw err;
        }
    }

    prepareAnalyzerDataForEvaluation(
        questionsData: QuestionDataRequiredForAnalyzer[],
        promptTemplates: TemplatesMap,
        translationCorrections: AnalyzerTextCorrection[],
        interviewSummaries: Record<number, string[]>,
        transcriptionsForProcessing: AnswerTranscription[],
        language: string,
        computedTraits: ComputedTrait[]
    ): AnalyzerDataForEvaluation {
        const questionsAnswerPairs: QuestionAnswerPairForAnalyzer[] = questionsData.map((questionItem) => {
            const answerForProcessing = transcriptionsForProcessing.find(
                (transcription) => transcription.questionId === questionItem.questionId
            )!;
            return {
                question: {
                    id: questionItem.questionId,
                    options: questionItem.questionOptions,
                    gradingPrinciples: questionItem.questionGradingPrinciples,
                    trait: {
                        id: questionItem.traitId,
                        options: questionItem.traitName,
                        traitSummariesMap: questionItem.traitSummariesMap,
                        metadata: questionItem.traitPromptMetadata,
                        familyTrait: {
                            id: questionItem.traitFamilyId,
                            options: questionItem.traitFamilyName,
                            metadata: questionItem.familyPromptMetadata,
                        },
                    },
                },
                answer: {
                    translatedTranscription: answerForProcessing.translatedTranscription,
                    transcription: answerForProcessing.transcription,
                    videoLink: answerForProcessing.url,
                },
            };
        });

        return {
            interviewsSummariesMap: interviewSummaries,
            questionAnswerPairs: questionsAnswerPairs,
            translationCorrections: translationCorrections,
            promptTemplates,
            language,
            computedTraits,
        };
    }

    async fetchComputedTraits(db: DB, ids: string[]): Promise<ComputedTrait[]> {
        try {
            const res = await db
                .select({
                    id: traits.id,
                    computedBy: traits.computedBy,
                })
                .from(traits)
                .where(and(inArray(traits.id, ids), isNotNull(traits.computedBy)));
            return res.map((item) => ({ id: item.id, computedBy: item.computedBy })) as ComputedTrait[];
        } catch (err) {
            this.logger.error("Error fetching computed traits", err);
            throw err;
        }
    }

    async fetchAndPrepareDataForEvaluation(
        questionsAndAnswersForProcessing: QuestionForProcessingAndAnswerVideoLink[],
        transcriptions: AnswerTranscription[],
        language: string,
        computedTraitsIds: string[]
    ): Promise<AnalyzerDataForEvaluation> {
        try {
            const db = await initDB();
            const [questionsData, promptTemplatesMap, translationCorrections, interviewSummaries, computedTraits] =
                await Promise.all([
                    this.fetchQuestionsData(
                        questionsAndAnswersForProcessing.map((question) => question.question_id!),
                        db
                    ),
                    this.getPromptTemplatesMap(db),
                    this.fetchTextCorrections(db, language),
                    this.fetchInterviewSummaries(db, language),
                    this.fetchComputedTraits(db, computedTraitsIds),
                ]);

            return this.prepareAnalyzerDataForEvaluation(
                questionsData,
                promptTemplatesMap,
                translationCorrections,
                interviewSummaries,
                transcriptions,
                language,
                computedTraits
            );
        } catch (err) {
            this.logger.error("Error fetching questions data", err);
            throw err;
        } finally {
            closeDBConnection();
        }
    }

    private createGradesMap(principles: GradingPrinciplesMap): Record<string, number> {
        return principles.en.reduce((acc, principle) => {
            acc[principle.description] = 0;
            return acc;
        }, {} as Record<string, number>);
    }

    async createReasoningModelCompletion(model: string, prompt: string): Promise<string> {
        const response = await this.openaiClient.chat.completions.create({
            model: model,
            messages: [
                {
                    role: "user",
                    content: prompt,
                },
            ],
        });

        this.logger.info(`Reasoning model completion response: ${response.choices[0].message.content}`);
        const content = response.choices[0].message.content ? response.choices[0].message.content : "";
        return content.includes(":") ? content.split(":")[1].trim() : content;
    }

    async createReasoningModelCompletionForScoreJudge(model: string, prompt: string): Promise<number> {
        const response = await this.openaiClient.chat.completions.create({
            reasoning_effort: "medium",
            model: model,
            messages: [{ role: "user", content: prompt }],
        });

        const content = JSON.parse(response.choices[0].message.content as unknown as string) as { score: string };
        this.logger.info(`Reasoning model completion for score judge response: ${content}`);
        return Number(content.score);
    }

    async createChatCompletion(
        model: string,
        systemContent: string,
        userContent: string,
        maxTokens: number,
        temperature: number
    ): Promise<string> {
        const body: ChatCompletionCreateParamsNonStreaming = {
            model: model,
            messages: [
                {
                    role: "system",
                    content: systemContent,
                },
                {
                    role: "user",
                    content: userContent,
                },
            ],
            max_tokens: maxTokens,
            temperature: temperature,
        };

        const response = await this.openaiClient.chat.completions.create(body);

        const content = response.choices[0].message.content ? response.choices[0].message.content : "";

        return content.includes(":") ? content.split(":")[1].trim() : content;
    }

    @retry()
    private async checkIfAnswerIsGradableByPrinciple(
        questionAnswerPair: QuestionAnswerPairForAnalyzer,
        promptTemplatesMap: TemplatesMap,
        customerId: string,
        idx: number
    ): Promise<boolean> {
        const questionText = questionAnswerPair.question.options.options[customerId]
            ? questionAnswerPair.question.options.options[customerId].en.male.text
            : questionAnswerPair.question.options.options.default.en.male.text;
        const attribute = questionAnswerPair.question.trait.options.en;
        const criterion = questionAnswerPair.question.gradingPrinciples.en[idx].description;

        const systemContent = promptTemplatesMap.en[this.shouldGradeAnswerTemplate]
            .replace("{question}", questionText)
            .replace("{attribute}", attribute)
            .replace("{criterion}", criterion);

        this.logger.info(`CHECKING IF GRADABLE BY FOR ATTRIBUT ${attribute} | FOR CRITERION ${criterion}`);

        this.logger.info(
            `Checking if answer is gradable by principle system content: ${systemContent} | user input: ${questionAnswerPair.answer.translatedTranscription}`
        );

        const response = await this.createChatCompletion(
            this.shouldGradeAnswerModel,
            systemContent,
            questionAnswerPair.answer.translatedTranscription,
            this.shouldGradeAnswerTokens,
            this.shouldGradeAnswerTemperature
        );

        this.logger.info(`Check if answer is gradable by principle - response: ${response}`);

        return response === "1";
    }

    async calculateTraitScore(
        gradesMap: Record<string, number>,
        questionAnswerPair: QuestionAnswerPairForAnalyzer,
        templatesMap: TemplatesMap,
        customerId: string
    ): Promise<number> {
        this.logger.info(
            `Calculating trait score for question ${JSON.stringify(
                questionAnswerPair.question.options.options.default.en.male.text
            )} and grades map ${JSON.stringify(gradesMap)}`
        );

        const gpsExplanations: string = questionAnswerPair.question.gradingPrinciples.en
            .map((principle, idx) => {
                const score = gradesMap[principle.description];
                if (score === 0) return this.calculateTraitScorePrincipleTemplateZeroScoreMessage;

                const scoreExplanation = principle.performance_levels_map[score > 5 ? 5 : score].text;

                return templatesMap.en[this.calculateTraitScorePrincipleTemplate]
                    .replace("{principle_nb}", String(idx + 1))
                    .replace("{principle_title}", principle.description)
                    .replace("{principle_grade}", String(score))
                    .replace("{principle_exp}", scoreExplanation);
            })
            .join("\n");

        const systemContent = templatesMap.en[this.calculateTraitScoreTemplate]
            .replace("{GPs}", gpsExplanations)
            .replace("{attribute}", questionAnswerPair.question.trait.options.en)
            .replace(
                "{question}",
                questionAnswerPair.question.options.options[customerId]
                    ? questionAnswerPair.question.options.options[customerId].en.male.text
                    : questionAnswerPair.question.options.options.default.en.male.text
            );

        const userContent = questionAnswerPair.answer.transcription;

        this.logger.info(`Trait Grading System content: ${systemContent}`);
        this.logger.info(`Trait Grading User content: ${userContent}`);

        const traitScore = await this.createChatCompletion(
            this.calculateTraitScoreModel,
            systemContent,
            userContent,
            this.calculateTraitScoreTokens,
            this.calculateTraitScoreTemperature
        );

        const finalScore = Number(traitScore) > 5 ? 5 : Number(traitScore) < 0 ? 0 : Number(traitScore);

        return finalScore;
    }

    @retry()
    async gradeAnswer(
        questionAnswerPair: QuestionAnswerPairForAnalyzer,
        principlesForGrading: GradingPrinciple[],
        templatesMap: TemplatesMap,
        customerId: string
    ): Promise<{ traitScore: number; gradesMap: Record<string, number> }> {
        try {
            const gradesMap = this.createGradesMap(questionAnswerPair.question.gradingPrinciples);

            await Promise.all(
                principlesForGrading.map(async (principle) => {
                    this.logger.info(`Grading principle - ${JSON.stringify(principle)}`);
                    const systemContent = templatesMap.en[this.gradeAnswerTemplate]
                        .replace("{attribute}", questionAnswerPair.question.trait.options.en)
                        .replace(
                            "{question}",
                            questionAnswerPair.question.options.options[customerId]
                                ? questionAnswerPair.question.options.options[customerId].en.male.text
                                : questionAnswerPair.question.options.options.default.en.male.text
                        )
                        .replace("{criterion}", principle.description)
                        .replace("{score_1}", principle.performance_levels_map["1"].text)
                        .replace("{score_2}", principle.performance_levels_map["2"].text)
                        .replace("{score_3}", principle.performance_levels_map["3"].text)
                        .replace("{score_4}", principle.performance_levels_map["4"].text)
                        .replace("{score_5}", principle.performance_levels_map["5"].text);

                    this.logger.info(
                        `GRADING PRINCIPLE SYSTEM CONTENT: ${systemContent} |
                         GRADING PRINCIPLE USER CONTENT: ${questionAnswerPair.answer.translatedTranscription} |
                         TRAIT: ${questionAnswerPair.question.trait.options.en} |
                         PRINCIPLE: ${principle.description}`
                    );

                    const content = await this.createChatCompletion(
                        this.gradeAnswerModel,
                        systemContent,
                        questionAnswerPair.answer.translatedTranscription,
                        this.gradeAnswerTokens,
                        this.gradeAnswerTemperature
                    );

                    this.logger.info(`Grading principle grade - content: ${content}`);

                    const score = Number(content);

                    gradesMap[principle.description] = score;
                })
            );

            this.logger.info(`Grades map: ${JSON.stringify(gradesMap)}`);

            const traitScore = await this.calculateTraitScore(gradesMap, questionAnswerPair, templatesMap, customerId);

            return { traitScore, gradesMap };
        } catch (err) {
            this.logger.error("Error grading answer", err);
            throw err;
        }
    }

    scoreToGradingExplanation(score: number, gradingPrincipe: GradingPrinciple): string {
        if (score === 0) return "Was not able to evaluate, not enough information has been provided";
        else if (score === 1 || score === 2)
            return (
                gradingPrincipe.description +
                "\n" +
                gradingPrincipe.performance_levels_map[1].text +
                "\n" +
                gradingPrincipe.performance_levels_map[2].text
            );
        else if (score === 3)
            return (
                gradingPrincipe.description +
                "\n" +
                Object.values(gradingPrincipe.performance_levels_map)
                    .map((item) => item.text)
                    .join("\n")
            );
        else if (score === 4 || score === 5)
            return (
                gradingPrincipe.description +
                "\n" +
                gradingPrincipe.performance_levels_map[5].text +
                "\n" +
                gradingPrincipe.performance_levels_map[4].text
            );

        throw new Error("Invalid trait score");
    }

    @retry()
    async generateEnglishTraitSummary(
        templatesMap: TemplatesMap,
        question: QuestionForAnalyzer,
        traitScore: number,
        language: string,
        gradesMap: Record<string, number>
    ): Promise<string> {
        try {
            this.logger.info(
                `Generating trait summary for trait ${question.trait.options.en
                } with score ${traitScore} and grades map ${JSON.stringify(gradesMap)} and language ${language}`
            );
            if (traitScore === 0) {
                const summaryChoices = [
                    "The interview did not provide enough information to evaluate the candidate's suitability for the position.",
                    "There was not enough information to assess the suitability of the candidate for the position.",
                ];
                return summaryChoices[Math.floor(Math.random() * summaryChoices.length)];
            }

            const psychologistSummary = question.trait.traitSummariesMap.en[traitScore]
                .map((summary) => summary.text)
                .join("\n");
            const psychologistGradingExplanation = question.gradingPrinciples.en
                .map((principle) =>
                    templatesMap.en[this.traitSummaryExplanationTemplate]
                        .replace("{score}", String(gradesMap[principle.description]))
                        .replace("{description}", principle.description)
                        .replace("{explanation}", this.scoreToGradingExplanation(traitScore, principle))
                )
                .join("\n");

            const intro = templatesMap.en[this.traitSummaryIntroTemplate]
                .replace("{trait}", question.trait.options.en)
                .replace("{score}", this.scoreToVerbalValue[language][traitScore])
                .replace("{psychologist_summary}", psychologistSummary)
                .replace("{grading_principle_explanation}", psychologistGradingExplanation)
                .replace("{composer}", templatesMap.en[this.traitSummaryIntroComposerTemplate]);

            this.logger.info(`Trait ${question.trait.options.en} summary INTRO: ${intro}`);

            const ending = templatesMap.en[this.traitSummaryEndingTemplate]
                .replace("{level_5}", this.scoreToVerbalValue[language][5])
                .replace("{level_4}", this.scoreToVerbalValue[language][4])
                .replace("{level_3}", this.scoreToVerbalValue[language][3])
                .replace("{level_2}", this.scoreToVerbalValue[language][2])
                .replace("{level_1}", this.scoreToVerbalValue[language][1]);

            this.logger.info(`Trait ${question.trait.options.en} summary ENDING: ${ending}`);

            const prompt = intro + "\n" + ending;

            this.logger.info(`Trait ${question.trait.options.en} summary: ${prompt}`);

            const response = await this.promptOpenAIChatModel(
                this.staticSystemInput,
                prompt,
                this.traitSummaryModel,
                this.traitSummaryTokens,
                this.traitSummaryTemperature
            );

            return response;
        } catch (err) {
            this.logger.error("Error generating trait summary", err);
            throw err;
        }
    }

    @retry()
    async generateHebrewTraitSummary(
        templatesMap: TemplatesMap,
        question: QuestionForAnalyzer,
        traitScore: number,
        language: string,
        gradesMap: Record<string, number>
    ): Promise<string> {
        try {
            this.logger.info(
                `Generating trait summary for trait ${question.trait.options.he
                } with score ${traitScore} and grades map ${JSON.stringify(gradesMap)} and language ${language}`
            );
            if (
                traitScore == null ||                // null or undefined
                traitScore === 0 ||                  // exactly zero
                isNaN(Number(traitScore)) ||         // NaN when converted to number
                String(traitScore).trim().toLowerCase() === 'nan' || // literal "NaN" string
                String(traitScore).trim() === ''     // empty string
            ) {
                const summaryChoices = [
                    "הראיון לא סיפק מספיק מידע להערכת התאמתו של המועמד לתפקיד.",
                    "לא היה מספיק מידע כדי להעריך את התאמת המועמד לתפקיד.",
                ];
                return summaryChoices[Math.floor(Math.random() * summaryChoices.length)];
            }

            const hebrewTraitSummaries = question.trait.traitSummariesMap.he[traitScore]
                .map((summary) => summary.text)
                .join("\n");
            const verbalScore = this.scoreToVerbalValue[language][traitScore];
            const explanation = question.gradingPrinciples.he
                .map((principle, idx) => {
                    return "{description} - {score} - {explanation}"
                        .replace("{description}", principle.description)
                        .replace("{score}", String(gradesMap[question.gradingPrinciples.en[idx].description]))
                        .replace("{explanation}", this.scoreToGradingExplanation(traitScore, principle));
                })
                .join("\n");

            const hebrewPrompt = templatesMap.he[this.traitSummaryHebrewTemplate]
                .replace("{trait}", question.trait.options.he)
                .replace("{score}", verbalScore)
                .replace("{trait_summaries}", hebrewTraitSummaries)
                .replace("{grading_principles_explanations}", explanation);

            this.logger.info(`GENERATING HEBREW TRAIT SUMMARY ${question.trait.options.he} summary: ${hebrewPrompt}`);

            const response = await this.promptOpenAIChatModel(
                this.staticSystemInput,
                hebrewPrompt,
                this.traitSummaryModel,
                this.traitSummaryTokens,
                this.traitSummaryTemperature
            );

            if (question.trait.metadata && question.trait.metadata.he.correctionLayerRemarks) {
                this.logger.info("There are correction layer remarks for this trait ${question.trait.options.he} ");
                this.logger.info(
                    `Generating correction layer remarks according remarks: ${question.trait.metadata.he.correctionLayerRemarks}`
                );

                const correctionLayerRemarks = question.trait.metadata.he.correctionLayerRemarks;
                const correctionLayerSystem = templatesMap.he[this.familySummaryCorrectionSystemTemplate];
                const correctionPrompt = templatesMap.he[this.familySummaryCorrectionTemplate]
                    .replace("{remarks}", correctionLayerRemarks)
                    .replace("{text}", response);

                this.logger.info(`Trait correction layer prompt: ${correctionPrompt}`);

                const responseWithCorrectionLayerRemarks = await this.promptOpenAIChatModel(
                    correctionLayerSystem,
                    correctionPrompt,
                    this.traitSummaryModel,
                    this.traitSummaryTokens,
                    this.traitSummaryTemperature
                );

                this.logger.info(
                    `Response with trait correction layer remarks for trait ${question.trait.options.he}: ${responseWithCorrectionLayerRemarks} `
                );

                return responseWithCorrectionLayerRemarks;
            } else {
                return response;
            }
        } catch (err) {
            this.logger.error("Error generating trait summary", err);
            throw err;
        }
    }

    async generateTraitSummary(
        templatesMap: TemplatesMap,
        question: QuestionForAnalyzer,
        traitScore: number,
        language: string,
        gradesMap: Record<string, number>
    ): Promise<string> {
        return language === "en"
            ? await this.generateEnglishTraitSummary(templatesMap, question, traitScore, language, gradesMap)
            : await this.generateHebrewTraitSummary(templatesMap, question, traitScore, language, gradesMap);
    }

    async handleGradableAnswer(
        questionAnswerPair: QuestionAnswerPairForAnalyzer,
        principlesForGrading: GradingPrinciple[],
        promptTemplatesMap: TemplatesMap,
        language: string,
        customerId: string
    ): Promise<AnalyzedAnswerResult> {
        const { gradesMap, traitScore } = await this.gradeAnswer(
            questionAnswerPair,
            principlesForGrading,
            promptTemplatesMap,
            customerId
        );

        const [summary, judgeScore] = await Promise.all([
            this.generateTraitSummary(promptTemplatesMap, questionAnswerPair.question, traitScore, language, gradesMap),
            this.judgeScore(
                traitScore,
                gradesMap,
                questionAnswerPair.question.options.options[customerId]
                    ? questionAnswerPair.question.options.options[customerId].en.male.text
                    : questionAnswerPair.question.options.options.default.en.male.text,
                questionAnswerPair.answer.translatedTranscription,
                questionAnswerPair.question.trait.options.en,
                promptTemplatesMap.en[this.judgeTemplate]
            ),
        ]);

        this.logger.info(
            `Trait:${questionAnswerPair.question.trait.options.en} summary: ${summary} Score: ${traitScore}`
        );

        return {
            gradesMap,
            judgeScore,
            score: traitScore,
            summary: summary,
            trait: questionAnswerPair.question.trait,
            videoLink: questionAnswerPair.answer.videoLink,
        };
    }

    async handleNotGradableAnswer(
        questionAnswerPair: QuestionAnswerPairForAnalyzer,
        promptTemplatesMap: TemplatesMap,
        customerId: string
    ): Promise<AnalyzedAnswerResult> {
        const gradesMap = this.createGradesMap(questionAnswerPair.question.gradingPrinciples);

        const judgeScore = await this.judgeScore(
            0,
            gradesMap,
            questionAnswerPair.question.options.options[customerId]
                ? questionAnswerPair.question.options.options[customerId].en.male.text
                : questionAnswerPair.question.options.options.default.en.male.text,
            questionAnswerPair.answer.translatedTranscription,
            questionAnswerPair.question.trait.options.en,
            promptTemplatesMap.en[this.judgeTemplate]
        );

        return {
            gradesMap,
            judgeScore,
            score: 0,
            summary: "",
            trait: questionAnswerPair.question.trait,
            videoLink: questionAnswerPair.answer.videoLink,
        };
    }

    async analyzeAnswer(
        questionAnswerPair: QuestionAnswerPairForAnalyzer,
        promptTemplatesMap: TemplatesMap,
        language: string,
        customerId: string
    ): Promise<AnalyzedAnswerResult> {
        if (questionAnswerPair.answer.translatedTranscription.length === 0) {


            this.logger.info("Answer is empty, skipping grading");
            this.logger.info(questionAnswerPair.answer)
            return {
                gradesMap: null,
                score: 0,
                summary: this.notGradableAnswerMessage[language]!,
                trait: questionAnswerPair.question.trait,
                videoLink: questionAnswerPair.answer.videoLink,
            };
        }

        const gradingPrinciples = questionAnswerPair.question.gradingPrinciples.en;

        const principlesForGrading: GradingPrinciple[] = [];

        for (const principle of gradingPrinciples) {
            const isGradable = await this.checkIfAnswerIsGradableByPrinciple(
                questionAnswerPair,
                promptTemplatesMap,
                customerId,
                gradingPrinciples.indexOf(principle)
            );
            this.logger.info(`PRINCIPLE GRADABILITY CHECK ${principle.description} is gradable: ${isGradable}`);
            if (isGradable) {
                principlesForGrading.push(principle);
            }
        }

        this.logger.info(`Grading principles: ${JSON.stringify(gradingPrinciples)}`);
        this.logger.info(`Gradable principles: ${JSON.stringify(principlesForGrading)}`);

        const shouldGrade = principlesForGrading.length >= gradingPrinciples.length / 2;

        if (shouldGrade) {
            return await this.handleGradableAnswer(
                questionAnswerPair,
                principlesForGrading,
                promptTemplatesMap,
                language,
                customerId
            );
        } else {
            this.logger.info(`handling not gradable answer:${questionAnswerPair.answer.translatedTranscription}`);
            return await this.handleNotGradableAnswer(questionAnswerPair, promptTemplatesMap, customerId);
        }
    }

    async analyzeAnswers(
        interviewDataForEvaluation: AnalyzerDataForEvaluation,
        customerId: string
    ): Promise<AnalyzedAnswerResult[]> {
        const { promptTemplates, questionAnswerPairs, language } = interviewDataForEvaluation;
        return await Promise.all(
            questionAnswerPairs.map((pair) => this.analyzeAnswer(pair, promptTemplates, language, customerId))
        );
    }

    groupAnswersByFamily(answers: AnalyzedAnswerResult[]): Record<string, AnalyzedAnswerResult[]> {
        return answers.reduce((acc, answer) => {
            if (!acc[answer.trait.familyTrait.options.en]) acc[answer.trait.familyTrait.options.en] = [];
            acc[answer.trait.familyTrait.options.en].push(answer);
            return acc;
        }, {} as Record<string, AnalyzedAnswerResult[]>);
    }

    replacePronouns(text: string): string {
        let textForCorrection = text;
        textForCorrection = textForCorrection.replace(/ he /g, " the candidate ").replace(/ He /g, " The candidate ");
        textForCorrection = textForCorrection.replace(/ she /g, " the candidate ").replace(/ She /g, " The candidate ");
        textForCorrection = textForCorrection.replace(/\.he /g, ".The candidate ").replace(/\.He /g, ".The candidate ");
        textForCorrection = textForCorrection
            .replace(/\.she /g, ".The candidate ")
            .replace(/\.She /g, ".The candidate ");
        textForCorrection = textForCorrection.replace(/"/g, "");
        textForCorrection = textForCorrection.replace(/role/g, "job");
        return textForCorrection;
    }

    async generateHebrewSummary(
        score: number,
        gender: string,
        answers: AnalyzedAnswerResult[],
        promptTemplatesMap: TemplatesMap
    ): Promise<string> {
        // Filtering out A18 - creativity trait out in case it's scored 3
        this.logger.info("---Generating Hebrew summary for answers---");

        const traitsForSummary = answers.filter((answer) => !(answer.trait.id === "A18" && answer.score === 3));

        const traitSummaries = traitsForSummary.map((answer) => answer.summary).join("\n");

        const traitNames = traitsForSummary.map((answer) => answer.trait.options.he).join(", ");

        this.logger.info(`Trait Names: ${traitNames}`);
        this.logger.info(`Family Name: ${answers[0].trait.familyTrait.options.he}`);
        this.logger.info(`Trait summaries: ${traitSummaries}`);

        const prompt = promptTemplatesMap.he[this.familySummaryTemplate]
            .replace("{traits_summaries}", traitSummaries)
            .replace("{traits}", traitNames)
            .replaceAll("{family_trait_name}", answers[0].trait.familyTrait.options.he);

        this.logger.info(`Family summary prompt: ${prompt}`);

        const summary = await this.promptOpenAIChatModel(
            this.staticSystemInput,
            prompt,
            this.familySummaryModel,
            this.familySummaryTokens,
            this.familySummaryTemperature
        );

        this.logger.info(`Family summary: ${summary}`);

        const remarks = answers[0].trait.familyTrait.metadata.he.correctionLayerRemarks.replace(
            "{score}",
            String(score)
        );

        this.logger.info(`Family remarks: ${remarks}`);

        const system = promptTemplatesMap.he[this.familySummaryCorrectionSystemTemplate].replace(
            "{family}",
            answers[0].trait.familyTrait.options.en
        );

        this.logger.info(`Family summary correction system: ${system}`);

        const correctionPrompt = promptTemplatesMap.he[this.familySummaryCorrectionTemplate]
            .replace("{remarks}", remarks)
            .replace("{text}", summary);

        this.logger.info(`Family summary correction prompt: ${correctionPrompt}`);

        const correctedSummary = await this.promptOpenAIChatModel(
            system,
            correctionPrompt,
            this.familySummaryModel,
            this.familySummaryCorrectionHebrewTokens,
            this.familySummaryCorrectionHebrewTemperature
        );

        if (gender === "female") {
            this.logger.info(`Corrected family summary: ${correctedSummary}`);

            const genderCorrection = promptTemplatesMap.he[this.familySummaryCorrectionLanguageGenderTemplate]
                .replace("{gender}", gender)
                .replace("{hebrew_text}", correctedSummary);

            const genderCorrectionSystem = this.familySummaryCorrectionLanguageGenderSystemContent;

            this.logger.info(`Gender correction system: ${genderCorrectionSystem}`);
            this.logger.info(`Gender correction prompt: ${genderCorrection}`);

            const correctedGenderSummary = await this.promptOpenAIChatModel(
                genderCorrectionSystem,
                genderCorrection,
                this.familySummaryModel,
                this.familySummaryGenderCorrectionTokens,
                this.familySummaryGenderCorrectionTemperature
            );

            return correctedGenderSummary;
        } else {
            return correctedSummary;
        }
    }

    async generateEnglishSummary(
        familyName: string,
        answers: AnalyzedAnswerResult[],
        promptTemplatesMap: TemplatesMap
    ): Promise<string> {
        const traitSummaries = answers.map((answer) => answer.summary).join("\n");

        const prompt = promptTemplatesMap.en[this.familySummaryTemplate]
            .replace("{traits_summaries}", traitSummaries)
            .replaceAll("{family_trait_name}", familyName);

        const summary = await this.promptOpenAIChatModel(
            this.staticSystemInput,
            prompt,
            this.familySummaryModel,
            this.familySummaryTokens,
            this.familySummaryTemperature
        );

        this.logger.info(`Family summary: ${summary}`);

        const system = promptTemplatesMap.en[this.familySummaryCorrectionSystemTemplate];

        const correction = await this.promptOpenAIChatModel(
            system,
            summary,
            this.familySummaryModel,
            this.familySummaryCorrectionEnglishTokens,
            this.familySummaryCorrectionEnglishTemperature
        );

        return this.replacePronouns(correction);
    }

    async scoreAndSummarizeFamily(
        answers: AnalyzedAnswerResult[],
        event: AnalyzerHandlerEvent,
        promptTemplatesMap: TemplatesMap
    ): Promise<Family> {
        const scoredAnswers = answers.filter((answer) => answer.score !== 0);
        const totalGradedAnswers = scoredAnswers.length;
        const isAllZero = totalGradedAnswers === 0;
        const family = answers[0].trait.familyTrait;

        if (isAllZero)
            return {
                summary: this.notGradableFamilyMessageMap[event.language as keyof LanguageVariation],
                code: family.id,
                score: 0,
            };

        this.logger.info(`Scoring and summarizing family ${family.options.en}`);
        this.logger.info(JSON.stringify(answers));

        const scoreAverage =
            answers.reduce((acc, answer) => {
                const score = answer.score;
                return acc + score;
            }, 0) / totalGradedAnswers;

        const familyName = answers[0].trait.familyTrait.options[event.language as keyof LanguageVariation];

        const summary =
            event.language === "en"
                ? await this.generateEnglishSummary(familyName, scoredAnswers, promptTemplatesMap)
                : await this.generateHebrewSummary(
                    scoreAverage,
                    event.gender ?? "male",
                    scoredAnswers,
                    promptTemplatesMap
                );

        return { summary, code: family.id, score: scoreAverage };
    }

    calculateInterviewScore(families: Family[]): number {
        return families.reduce((acc, family) => acc + family.score, 0) / families.length;
    }

    generateInterviewSummarySentence(
        interviewScore: number,
        interviewSummarySentencesMap: Record<number, string[]>
    ): string {
        const roundedScore = Math.round(interviewScore);
        const sentences = interviewSummarySentencesMap[roundedScore];

        if (!sentences || sentences.length === 0) {
            this.logger.warn(`No summary sentences found for score ${roundedScore}, using default fallback`);

            const availableScores = Object.keys(interviewSummarySentencesMap)
                .map(Number)
                .filter(
                    (score) => interviewSummarySentencesMap[score] && interviewSummarySentencesMap[score].length > 0
                );

            if (availableScores.length > 0) {
                const closestScore = availableScores.reduce((prev, curr) =>
                    Math.abs(curr - roundedScore) < Math.abs(prev - roundedScore) ? curr : prev
                );

                const fallbackSentences = interviewSummarySentencesMap[closestScore];
                return fallbackSentences[Math.floor(Math.random() * fallbackSentences.length)];
            }

            return "The interview has been analyzed successfully.";
        }

        return sentences[Math.floor(Math.random() * sentences.length)];
    }

    createReport(
        families: Family[],
        analyzedAnswers: AnalyzedAnswerResult[],
        computedTraits: Trait[],
        interviewSummarySentencesMap: Record<number, string[]>
    ): ReportVersion {
        this.logger.info(`Creating report for families: ${JSON.stringify(families)}`);
        const interviewScore = this.calculateInterviewScore(families);
        const interviewSummary = this.generateInterviewSummarySentence(interviewScore, interviewSummarySentencesMap);

        const report: ReportVersion = {
            summary: interviewSummary,
            interview_score: interviewScore,
            families,
            traits: [
                ...analyzedAnswers.map((answer) => ({
                    judgeScore: answer.judgeScore,
                    trait: answer.trait.options,
                    code: answer.trait.id,
                    score: answer.score,
                    summary: answer.summary,
                    video_link: answer.videoLink,
                    grading_map: answer.gradesMap,
                })),
                ...computedTraits,
            ],
        };

        this.logger.info(`GENERATED REPORT: ${JSON.stringify(report)}`);

        return report;
    }

    async saveReport(report: ReportVersion, event: AnalyzerHandlerEvent, db: DB): Promise<void> {
        try {
            await db
                .update(interviews)
                .set({
                    analyzerReport: { report_versions: [report] },
                    status: "completed",
                })
                .where(eq(interviews.id, event.interview_id));
        } catch (err) {
            this.logger.error("Error saving report", err);
            throw err;
        }
    }

    async saveAnswers(
        analyzedAnswers: AnswerTranscription[],
        openQuestionsAnswers: AnswerTranscription[],
        warmupQuestionsAnswers: AnswerTranscription[],
        event: AnalyzerHandlerEvent,
        db: DB
    ): Promise<void> {
        const allAnswers = [...warmupQuestionsAnswers, ...analyzedAnswers, ...openQuestionsAnswers];

        const answersToSave: { interviewId: string; answer: InterviewQuestionAnswer }[] = allAnswers.map((item) => {
            return {
                interviewId: event.interview_id,
                answer: {
                    answer: item.transcription,
                    question_id: item.questionId,
                    question: item.question,
                    video_link: item.url,
                    translated_answer: item.translatedTranscription,
                },
            };
        });

        try {
            await db.delete(answers).where(eq(answers.interviewId, event.interview_id));
            await db.insert(answers).values(answersToSave);
        } catch (err) {
            this.logger.error("Error saving answers", err);
            throw err;
        }
    }

    @retry()
    async saveAnalyzedDataAndNotifySuccess(
        transcriptionsForProcessing: AnswerTranscription[],
        openQuestionsTranscriptions: AnswerTranscription[],
        warmupQuestionsTranscriptions: AnswerTranscription[],
        event: AnalyzerHandlerEvent,
        report: ReportVersion
    ): Promise<void> {
        try {
            const db = await initDB();

            await db.transaction(async (trx) => {
                await Promise.all([
                    this.saveReport(report, event, trx),
                    this.saveAnswers(
                        transcriptionsForProcessing,
                        openQuestionsTranscriptions,
                        warmupQuestionsTranscriptions,
                        event,
                        trx
                    ),
                ]);
            });

            await this.notifySuccess(event, db);
        } catch (err) {
            this.logger.error("Error saving interview evaluation data", err);

            throw err;
        } finally {
            await closeDBConnection();
        }
    }

    @retry()
    async setInterviewStatusToFailed(event: AnalyzerHandlerEvent): Promise<void> {
        try {
            const db = await initDB();
            await db.update(interviews).set({ status: "failed" }).where(eq(interviews.id, event.interview_id));
        } catch (err) {
            this.logger.error("Error setting interview status to failed", err);
            throw err;
        } finally {
            closeDBConnection();
        }
    }

    async transcriptInterview(
        questionAnswerPairs: QuestionForProcessingAndAnswerVideoLink[],
        interviewId: string,
        language: string
    ): Promise<{
        transcriptionsForProcessing: AnswerTranscription[];
        openQuestionsTranscriptions: AnswerTranscription[];
        warmupQuestionsTranscriptions: AnswerTranscription[];
        questionsForProcessing: QuestionForProcessingAndAnswerVideoLink[];
    }> {
        try {
            const { questionsForProcessing, openQuestions, warmupQuestions } =
                this.splitAnswersForProcessingAndNotProcessing(questionAnswerPairs);

            const [transcriptionsForProcessing, openQuestionsTranscriptions, warmupQuestionsTranscriptions] =
                await Promise.all([
                    this.transcriptAnswers(questionsForProcessing, language, interviewId),
                    this.transcriptAnswers(openQuestions, language, interviewId),
                    this.transcriptAnswers(warmupQuestions, language, interviewId),
                ]);
            return {
                transcriptionsForProcessing,
                questionsForProcessing,
                openQuestionsTranscriptions,
                warmupQuestionsTranscriptions,
            };
        } catch (err) {
            this.logger.error("Error transcribing answers", err);
            throw err;
        }
    }

    async analyzeFamilies(
        analyzedAnswers: AnalyzedAnswerResult[],
        promptTemplatesMap: TemplatesMap,
        event: AnalyzerHandlerEvent
    ): Promise<Family[]> {
        try {
            const groupedByFamilyAnswers = this.groupAnswersByFamily(analyzedAnswers);

            this.logger.info(`Grouped by family answers: ${JSON.stringify(groupedByFamilyAnswers)}`);

            return await Promise.all(
                Object.values(groupedByFamilyAnswers).map((answers) =>
                    this.scoreAndSummarizeFamily(answers, event, promptTemplatesMap)
                )
            );
        } catch (err) {
            this.logger.error("Error analyzing families", err);
            throw err;
        }
    }

    async fetchJobNameAndCustomerName(
        interviewId: string,
        db: DB
    ): Promise<{ jobName: string; customerName: string; candidateName: string }> {
        try {
            const res = await db
                .select({
                    jobName: jobs.jobTitle,
                    customerName: customers.companyName,
                    candidateName: candidates.name,
                })
                .from(interviews)
                .innerJoin(candidates, eq(candidates.id, interviews.candidateId))
                .innerJoin(jobs, eq(jobs.id, candidates.jobId))
                .innerJoin(customers, eq(customers.id, jobs.customerId))
                .where(eq(interviews.id, interviewId));
            return res[0];
        } catch (err) {
            this.logger.error("Error fetching job name and customer name", err);
            throw err;
        }
    }

    async notifySuccess(event: AnalyzerHandlerEvent, db: DB): Promise<void> {
        try {
            const { jobName, customerName, candidateName } = await this.fetchJobNameAndCustomerName(
                event.interview_id,
                db
            );
            const env = process.env.ENV!;
            const region = process.env.AWS_REGION!;
            const topicArn = process.env.SUCCESS_TOPIC_ARN!;
            const clientBaseUrl = process.env.CLIENT_BASE_URL!;

            const message = `Evaluation has been completed for candidate: ${candidateName} \n\n
            Job: ${jobName} \n
            Customer: ${customerName} \n
            Interview Page Link: ${clientBaseUrl}/interviews/${event.interview_id}`;

            const sanitizeForSubject = (str: string): string => {
                if (!str) return '';
                return str.replace(/[^\x20-\x7E]/g, '').trim();
            };

            const sanitizedCandidateName = sanitizeForSubject(candidateName);
            const sanitizedJobName = sanitizeForSubject(jobName);
            const sanitizedCustomerName = sanitizeForSubject(customerName);

            let subject = `Evaluation completed (${env}) for ${sanitizedCandidateName} - ${sanitizedJobName} - ${sanitizedCustomerName}`;

            if (subject.length > 100) {
                subject = subject.substring(0, 97) + '...';
            }

            this.logger.info(`Sending SNS message to topic ${topicArn} with message ${message}`);
            this.logger.info(`Subject: ${subject}`);

            await sendSNSMessage({ message, region, subject, topicArn });
        } catch (err) {
            this.logger.error("Error notifying success", err);
            throw err;
        }
    }

    @retry()
    async notifyFailure(event: AnalyzerHandlerEvent, error: any): Promise<void> {
        const region = process.env.AWS_REGION!;
        const topicArn = process.env.ERROR_TOPIC_ARN!;
        const env = process.env.ENV!;
        const message = `Evaluation has failed for candidate ${event.candidate_name} \n\n 
        InterviewId: ${event.interview_id} \n
        Event: ${JSON.stringify(event)} \n
        Error: ${error.message} \n
        Error Stack: ${error.stack}
        `;
        const subject = `Evaluation failed (${env}) for ${event.candidate_name}`;

        await sendSNSMessage({ message, region, subject, topicArn });
    }

    private getQuestionText(
        questionAnswerPair: QuestionAnswerPairForAnalyzer,
        customerId: string,
        language: keyof LanguageVariation
    ): string {
        const options =
            questionAnswerPair.question.options.options[customerId] ||
            questionAnswerPair.question.options.options.default;
        return options[language] ? options[language].male.text : options.en.male.text;
    }

    async processComputedTraits(
        interviewDataForEvaluation: AnalyzerDataForEvaluation,
        customerId: string
    ): Promise<Trait[]> {
        this.logger.info(`Processing computed traits: ${JSON.stringify(interviewDataForEvaluation.computedTraits)}`);

        if (!interviewDataForEvaluation.computedTraits.length) return [];

        const traits: Trait[] = [];
        for (const trait of interviewDataForEvaluation.computedTraits) {
            const pairsForEvaluation = interviewDataForEvaluation.questionAnswerPairs.filter(
                (pair) => pair.answer.transcription.length > 100
            );

            this.logger.info(`PAIRS FOR EVALUATION COMPUTED TRAIT: ${JSON.stringify(pairsForEvaluation)}`);
            if (pairsForEvaluation.length === 0) continue;

            const dialog: string = pairsForEvaluation
                .map((pair) => {
                    return `${this.getQuestionText(
                        pair,
                        customerId,
                        interviewDataForEvaluation.language as keyof LanguageVariation
                    )} - ${pair.answer.transcription}`;
                })
                .join(" \n");

            const language = interviewDataForEvaluation.language;

            const template =
                interviewDataForEvaluation.promptTemplates[language as Language][this.verbalAbilityTemplate];

            const prompt = template.replace("{dialog}", dialog);

            this.logger.info(`COMPUTED TRAIT INPUT PROMPT: ${prompt}`);

            const response = await this.createReasoningModelCompletion(this.verbalAbilityModel, prompt);

            this.logger.info(`COMPUTED TRAIT RESPONSE ${response}`);

            const score = Number(response);

            traits.push({ code: trait.id, score, summary: "", video_link: "", grading_map: null });
        }

        this.logger.info(`Computed traits: ${JSON.stringify(traits)}`);

        return traits;
    }

    async judgeScore(
        score: number,
        gradesMap: Record<string, number>,
        question: string,
        answer: string,
        traitName: string,
        template: string
    ): Promise<number> {
        this.logger.info(
            `Judging score for trait ${traitName} with score ${score} and grades map ${JSON.stringify(
                gradesMap
            )} and question ${question} and answer ${answer}`
        );
        const gradingDetails = Object.entries(gradesMap).map(([key, value]) => {
            return `${key} - ${value}`;
        });

        this.logger.info(`Grading details: ${gradingDetails.join("\n")}`);
        const prompt = template
            .replace("{question}", question)
            .replace("{answer}", answer)
            .replace("{grading_details}", gradingDetails.join("\n"))
            .replace("{score_iverse}", score.toString())
            .replace("{attribute}", traitName);

        this.logger.info(`Judge prompt: ${prompt}`);
        const response = await this.createReasoningModelCompletionForScoreJudge(this.judgeModel, prompt);

        this.logger.info(`Judge response: ${response}`);
        return response;
    }

    async analyze(event: AnalyzerHandlerEvent): Promise<void> {
        try {
            this.logger.info(`Analyzing answers for interview ${JSON.stringify(event)}`);
            await this.changeInterviewStatusToRunning(event.interview_id);

            const {
                transcriptionsForProcessing,
                openQuestionsTranscriptions,
                warmupQuestionsTranscriptions,
                questionsForProcessing,
            } = await this.transcriptInterview(event.answer_and_question_pairs, event.interview_id, event.language);

            const interviewDataForEvaluation = await this.fetchAndPrepareDataForEvaluation(
                questionsForProcessing,
                transcriptionsForProcessing,
                event.language,
                event.computedTraits
            );

            const analyzedAnswers = await this.analyzeAnswers(interviewDataForEvaluation, event.customer_id);

            const [families, processedComputedTraits] = await Promise.all([
                this.analyzeFamilies(analyzedAnswers, interviewDataForEvaluation.promptTemplates, event),
                this.processComputedTraits(interviewDataForEvaluation, event.customer_id),
            ]);

            const report = this.createReport(
                families,
                analyzedAnswers,
                processedComputedTraits,
                interviewDataForEvaluation.interviewsSummariesMap
            );

            await this.saveAnalyzedDataAndNotifySuccess(
                transcriptionsForProcessing,
                openQuestionsTranscriptions,
                warmupQuestionsTranscriptions,
                event,
                report
            );
        } catch (err: any) {
            this.logger.error("Error analyzing answers", err);

            this.logger.error(err.stack);

            await this.setInterviewStatusToFailed(event);

            await this.notifyFailure(event, err);

            throw err;
        } finally {
            this.cleanResources();
        }
    }
}
