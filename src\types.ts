import { FamilyPromptMetadata, GradingPrinciplesMap, LanguageVariation, QuestionOptions, TraitForAnalyzer, TraitPromptMetadata, TraitSummariesMap } from "./common";

export interface QuestionDataRequiredForAnalyzer {
    questionOptions: QuestionOptions;
    traitName: LanguageVariation;
    traitFamilyName: LanguageVariation;
    questionId: string;
    traitId: string;
    traitFamilyId: string;
    traitSummariesMap: TraitSummariesMap;
    questionGradingPrinciples: GradingPrinciplesMap;
    traitPromptMetadata: TraitPromptMetadata;
    familyPromptMetadata: FamilyPromptMetadata;
}

export interface AnswerTranscription {
    url: string;
    transcription: string;
    translatedTranscription: string;
    question: string;
    questionId: string | null;
}

export interface AnalyzedAnswerResult {
    score: number;
    summary: string;
    videoLink: string;
    trait: TraitForAnalyzer;
    gradesMap: Record<string, number> | null;
    judgeScore?: number;
}
