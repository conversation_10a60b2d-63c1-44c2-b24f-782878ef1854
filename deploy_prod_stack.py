import subprocess


def deploy_sam_prod_stack():
    stack_name = "analyzer-prod"

    prompt = input(f"Do you want to deploy the {stack_name} stack? (yes/no): ")
    if prompt.lower() == 'yes':
        build_command = ["sam", "build"]

        deploy_command = [
            "sam", "deploy",
            "--stack-name", stack_name,
            "--capabilities", "CAPABILITY_IAM",
            "--parameter-overrides",
            "EnvironmentType=prod",
            "Region=us-east-1",
            "Bucket=analyzer-prod",
            "AnalyzerQueueName=prod_analyzer.fifo",
            "CloudFrontPrefix=https://d75yyi0td3j27.cloudfront.net",
            "--no-fail-on-empty-changeset",
            "--region", "us-east-1"
        ]

        try:
            print('Building and deploying the stack...')
            subprocess.run(build_command, check=True)
            print("Build successful.")
            subprocess.run(deploy_command, check=True)
            print("Deployment successful.")
        except subprocess.CalledProcessError as e:
            print("Failed to deploy:", e)
    else:
        print("Exiting without deploying the dev stack.")
        return


deploy_sam_prod_stack()
